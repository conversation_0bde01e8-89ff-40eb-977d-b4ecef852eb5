# ESP32-C3 多功能遥控信号处理器完整使用说明

本项目基于ESP32-C3开发板，支持四种不同的遥控信号处理模式。通过PlatformIO的环境配置，可以选择编译不同的功能。

**重要说明**: SBUS和IBUS使用独立的引脚，避免信号冲突，确保稳定工作。

## 支持的功能

### 1. SBUS信号测试打印
- **环境名称**: `sbus_test`
- **源文件**: `src/main.cpp`
- **功能**: 读取SBUS信号，通过串口打印所有通道数据和状态信息
- **用途**: 调试SBUS信号，监控遥控器输入，检查失联和故障安全状态

### 2. IBUS信号测试打印
- **环境名称**: `ibus_test`
- **源文件**: `src/main_ibus_in.cpp`
- **功能**: 读取富斯IA6B接收机的IBUS信号，通过串口打印所有通道数据
- **用途**: 调试IBUS信号，监控遥控器输入，检查接收计数器

### 3. SBUS转IBUS信号转换
- **环境名称**: `sbus_to_ibus`
- **源文件**: `src/main_sbus_to_ibus.cpp`
- **功能**: 读取SBUS信号并转换成IBUS格式输出
- **用途**: 将SBUS接收机信号转换为IBUS格式，供支持IBUS的设备使用

### 4. SBUS差速小车控制 ⭐
- **环境名称**: `ardu_sbus_scout`
- **源文件**: `src/main_ardu_sbus_scout.cpp`
- **功能**: 读取飞控SBUS信号中的CH1(左油门)和CH3(右油门)，转换为差速小车控制信号
- **用途**: 将双摇杆控制转换为单摇杆差速转弯控制，适用于差速驱动小车

#### SBUS差速小车控制详细说明

**控制逻辑**：
- **输入信号**：
  - Channel 1: 左侧油门控制量（模拟左轮速度）
  - Channel 3: 右侧油门控制量（模拟右轮速度）
- **输出信号**：
  - Channel 1: 转弯控制量（差速算法处理）
  - Channel 3: 前进后退控制量（差速算法处理）
  - 其他通道: 保持输入值，转换到输出范围240~1808

**转换算法**：
```
输入范围: SBUS标准范围 172~1811
输出范围: 240~1808 (保持原有输出范围)
中位值: 1024

转换过程:
1. Channel1和Channel3差速处理:
   - 将输入SBUS值转换为 1000 到 2000 的标准化范围
   - 应用死区处理，中位值1500，消除摇杆抖动
   - 差速控制算法:
     * 前进后退 = (左油门 + 右油门) / 2
     * 转弯控制 = (右油门 - 左油门) / 2
   - 转换为输出SBUS范围 240~1808

2. 其他通道处理:
   - 直接将输入范围172~1811映射到输出范围240~1808
   - 保持原有控制逻辑不变
```

**控制效果示例**：
1. **直线前进**: 左油门=1500, 右油门=1500 → 转弯=1024(中位), 前进后退=1500(前进)
2. **右转前进**: 左油门=1300, 右油门=1700 → 转弯=1224(右转), 前进后退=1500(前进)
3. **原地右转**: 左油门=1200, 右油门=1800 → 转弯=1248(右转), 前进后退=1500(停止)
4. **左转后退**: 左油门=1100, 右油门=900 → 转弯=924(左转), 前进后退=1000(后退)

**安全特性**：
- 故障安全检测：检测SBUS信号失联状态和故障安全标志
- 失联处理：失联时Channel1和Channel3输出中位值，停止小车运动，其他通道保持最后有效值
- 死区处理：中位值1500附近±50范围内视为停止，消除摇杆抖动影响

## 编译和上传

### 使用PlatformIO命令行

```bash
# 编译SBUS信号测试
pio run -e sbus_test

# 编译IBUS信号测试
pio run -e ibus_test

# 编译SBUS转IBUS转换器
pio run -e sbus_to_ibus

# 编译SBUS差速小车控制
pio run -e ardu_sbus_scout

# 上传到开发板 (以IBUS测试为例)
pio run -e ibus_test -t upload

# 监控串口输出
pio device monitor
```

### 使用VSCode PlatformIO插件

1. 打开VSCode，确保安装了PlatformIO插件
2. 在底部状态栏选择对应的环境：
   - `sbus_test` - SBUS信号测试打印
   - `ibus_test` - IBUS信号测试打印
   - `sbus_to_ibus` - SBUS转IBUS转换
   - `ardu_sbus_scout` - SBUS差速小车控制
3. 点击"Build"按钮编译
4. 点击"Upload"按钮上传到开发板

## 引脚连接

### ESP32-C3引脚定义

```
SBUS_RX_PIN = 7    // SBUS信号输入（专用）
SBUS_TX_PIN = 6    // SBUS信号输出（未使用）
IBUS_RX_PIN = 4    // IBUS信号输入（专用，与SBUS分离）
IBUS_TX_PIN = 3    // IBUS信号输出（专用）
LED_PIN = 8        // 状态指示LED
```

### 连接说明

#### SBUS信号测试模式

- 将SBUS接收机的信号线连接到GPIO7
- LED连接到GPIO8，指示数据接收状态

#### IBUS信号测试模式

- 将富斯IA6B接收机的IBUS线连接到GPIO4
- 如需双向通信，GPIO3连接到IBUS线（需要二极管隔离）
- LED连接到GPIO8，指示数据接收状态

#### SBUS转IBUS模式

- SBUS输入：GPIO7（连接SBUS接收机）
- IBUS输出：GPIO3（连接IBUS设备）
- 可以同时连接SBUS接收机和IBUS设备，实现信号转换

#### SBUS差速小车控制模式

- SBUS输入：GPIO20（连接飞控SBUS输出）
- SBUS输出：GPIO21（连接差速小车控制器）
- LED连接到GPIO8，指示数据处理状态
- 控制逻辑：CH1(左油门) + CH3(右油门) → CH1(转弯) + CH3(前进后退)

## 串口监控

所有模式都支持115200波特率的串口调试输出：

### SBUS测试模式输出示例
```
SBUS通道: CH1:372 CH2:1001 CH3:1630 CH4:1001 CH5:1001 CH6:1001 CH7:1001 CH8:1001 | 失联:否 故障安全:否
```

### IBUS测试模式输出示例

```
IBUS通道: CH1:1500 CH2:1500 CH3:1000 CH4:1500 CH5:1500 CH6:1500 CH7:1500 CH8:1500 CH9:1500 CH10:1500 | 接收计数: 123 传感器计数: 0
```

### SBUS转IBUS模式输出示例

```
SBUS->IBUS: CH1:372->1000 CH2:1001->1500 CH3:1630->2000 CH4:1001->1500 CH5:1001->1500 CH6:1001->1500
```

### SBUS差速小车控制模式输出示例
```
输入 - 左油门:1300 右油门:1700 | 输出 - 转弯:1224 前进后退:1500 | 其他通道 - CH2:1500->1524 | 状态 - 失联:否 故障安全:否
```

**输出说明**：
- **左油门/右油门**: 原始SBUS输入值
- **转弯**: 转换后的转弯控制值
- **前进后退**: 转换后的前进后退控制值
- **其他通道**: 显示输入值->输出值的转换情况
- **失联**: SBUS信号是否失联
- **故障安全**: 是否触发故障安全模式

## 库依赖

项目自动管理以下库依赖：

- **Bolder Flight Systems SBUS** (v8.1.4): SBUS协议处理
- **IBusBM** (v1.1.4): IBUS协议处理

## 故障排除

### 编译错误

1. 确保选择了正确的环境
2. 检查库依赖是否正确安装
3. 清理构建缓存：`pio run -t clean`

### 无信号输入

1. 检查引脚连接是否正确
2. 确认接收机已正确绑定遥控器
3. 检查波特率设置（SBUS: 100000, IBUS: 115200）

### SBUS差速控制问题

1. **控制方向错误**: 检查左右油门通道映射，调整差速算法中的符号
2. **响应不灵敏**: 调整死区参数DEAD_ZONE
3. **信号不稳定**: 检查电源供应质量，确认SBUS信号线屏蔽良好

### 串口无输出

1. 确认串口波特率设置为115200
2. 检查USB连接
3. 在Windows上可能需要安装CH340驱动

## 技术参数

- **开发板**: Seeed Studio XIAO ESP32C3
- **工作电压**: 3.3V
- **SBUS波特率**: 100000 bps, 8E2
- **IBUS波特率**: 115200 bps, 8N1
- **PWM频率**: 1000Hz
- **PWM分辨率**: 12位
- **通道范围**: 1000-2000μs (标准遥控范围)
- **处理延迟**: <1ms
- **更新频率**: 约50Hz (跟随SBUS输入频率)
- **死区范围**: ±50 (可调整)
- **工作温度**: -10°C ~ +60°C

## 应用场景

### SBUS差速小车控制的典型应用
1. **遥控坦克/履带车**: 直接使用差速转向
2. **四轮差速小车**: 左右轮独立控制
3. **机器人底盘**: 差速驱动移动平台
4. **教学演示**: 控制算法学习和验证
5. **竞赛机器人**: 精确的差速控制

## 安全注意事项

1. **信号连接**: 确保SBUS信号线正确连接，注意信号反相
2. **电源供应**: 确保ESP32-C3供电稳定
3. **接地连接**: SBUS设备间需要共地连接
4. **调试监控**: 建议在调试时监控串口输出，确认控制逻辑正确
5. **安全测试**: 在实际使用前，建议先进行台架测试，确认控制方向正确
6. **故障安全**: 确保在信号失联时小车能够安全停止

## 项目优势

1. **稳定性**: 独立引脚避免信号冲突
2. **灵活性**: 多环境配置支持不同功能
3. **易用性**: 详细的调试输出和文档
4. **扩展性**: 清晰的代码结构便于扩展
5. **兼容性**: 支持主流的SBUS和IBUS协议
6. **安全性**: 完整的故障安全和失联保护机制
