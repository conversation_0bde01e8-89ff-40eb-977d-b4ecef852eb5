#include <Arduino.h>
#include <HardwareSerial.h>
#include "sbus.h"

// SBUS差速小车控制程序
// 输入：飞控SBUS信号中的Channel1(油门左)和Channel2(油门右)
// 输出：差速转弯控制信号 - 通道1(左右)，通道3(前后)



// 死区设置
const int DEAD_ZONE = 10; // 中位死区

// 限幅函数
int valueClamp(int value, int minValue, int maxValue)
{
    if (value < minValue)
        return minValue;
    if (value > maxValue)
        return maxValue;
    return value;
}

// 死区处理函数
int applyDeadZone(int value, int center, int deadZone)
{
    if (abs(value - center) < deadZone)
    {
        return center;
    }
    return value;
}

// SBUS信号转换函数（带死区）- 参考main_sbus_in.cpp.txt
int convertSbusValue(int inputValue)
{
    // 输入范围参数
    const int INPUT_MIN = 1000;
    const int INPUT_MAX = 2000;
    const int INPUT_CENTER = 1001;

    // 输出范围参数
    const int OUTPUT_MIN = 240;
    const int OUTPUT_MAX = 1808;
    const int OUTPUT_CENTER = 1024;

    // 死区参数（中位值附近±死区范围内输出中位值）
    const int DEADZONE = 10; // 死区大小，可根据需要调整

    // 限制输入值在有效范围内
    if (inputValue < INPUT_MIN)
        inputValue = INPUT_MIN;
    if (inputValue > INPUT_MAX)
        inputValue = INPUT_MAX;

    // 死区处理：中位值附近直接输出中位值
    if (abs(inputValue - INPUT_CENTER) <= DEADZONE)
    {
        return OUTPUT_CENTER;
    }

    int outputValue;

    if (inputValue < INPUT_CENTER - DEADZONE)
    {
        // 处理低半段 (INPUT_MIN ~ INPUT_CENTER-DEADZONE) -> (OUTPUT_MIN ~ OUTPUT_CENTER)
        outputValue = map(inputValue, INPUT_MIN, INPUT_CENTER - DEADZONE, OUTPUT_MIN, OUTPUT_CENTER);
    }
    else if (inputValue > INPUT_CENTER + DEADZONE)
    {
        // 处理高半段 (INPUT_CENTER+DEADZONE ~ INPUT_MAX) -> (OUTPUT_CENTER ~ OUTPUT_MAX)
        outputValue = map(inputValue, INPUT_CENTER + DEADZONE, INPUT_MAX, OUTPUT_CENTER, OUTPUT_MAX);
    }

    return outputValue;
}

// 差速控制算法
// 输入：油门左(throttle_left)，油门右(throttle_right)
// 输出：左右控制(turn_output)，前后控制(throttle_output)
void differentialToTankControl(int throttle_left, int throttle_right, int &turn_output, int &throttle_output)
{
    // 使用转换函数处理输入值（带死区）
    int left_converted = convertSbusValue(throttle_left);
    int right_converted = convertSbusValue(throttle_right);

    // 将转换后的值映射到-500到+500的范围进行计算
    int left_calc = map(left_converted, 240, 1808, -500, 500);
    int right_calc = map(right_converted, 240, 1808, -500, 500);

    // 差速控制算法
    // 前进后退 = (左 + 右) / 2
    // 转弯 = (右 - 左) / 2
    int throttle_calc = (left_calc + right_calc) / 2;
    int turn_calc = (right_calc - left_calc) / 2;

    // 转换回SBUS输出范围
    throttle_output = map(throttle_calc, -500, 500, 240, 1808);
    turn_output = map(turn_calc, -500, 500, 240, 1808);

    // 限幅
    throttle_output = valueClamp(throttle_output, 240, 1808);
    turn_output = valueClamp(turn_output, 240, 1808);
}

typedef struct
{
    int8_t sbus_rx_pin;
    int8_t sbus_tx_pin;
    int8_t led_pin;
} PinDefinition;

// 结构体实例化 - 独立的SBUS引脚
PinDefinition pin_definition = {
    .sbus_rx_pin = 20, // SBUS专用接收引脚
    .sbus_tx_pin = 21, // SBUS专用发送引脚
    .led_pin = 8       // 状态指示LED
};

HardwareSerial SerialSbus(1);  // 硬件串口1用于SBUS输入和输出

bfs::SbusRx sbus_rx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusTx sbus_tx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusData sbus_data;
bfs::SbusData sbus_tx_data; // 用于发送的SBUS数据

void setup()
{
    // 初始化USB串口用于调试输出
    Serial.begin(115200);
    delay(1000); // 等待串口初始化完成
    pinMode(pin_definition.led_pin, OUTPUT);

    // 初始化SBUS通信
    sbus_rx.Begin();
    sbus_tx.Begin();

    // 初始化输出数据为中位值
    for (int i = 0; i < 16; i++)
    {
        sbus_tx_data.ch[i] = 1024;
    }
    sbus_tx_data.lost_frame = false;
    sbus_tx_data.failsafe = false;

    Serial.println("=== SBUS差速小车控制器 ===");
    Serial.println("读取飞控SBUS信号并转换为差速控制...");
    Serial.println("引脚配置:");
    Serial.print("  SBUS RX: GPIO");
    Serial.println(pin_definition.sbus_rx_pin);
    Serial.print("  SBUS TX: GPIO");
    Serial.println(pin_definition.sbus_tx_pin);
    Serial.print("  LED: GPIO");
    Serial.println(pin_definition.led_pin);
    Serial.println("控制映射:");
    Serial.println("  输入: CH1(油门左) + CH2(油门右)");
    Serial.println("  输出: CH1(左右控制) + CH3(前后控制)");
    Serial.println("  输出范围: 240~1808, 中位值: 1024");
    Serial.println("============================");
}

void loop()
{
    static unsigned long lastPrint = 0;
    static unsigned long lastLedToggle = 0;
    static bool ledState = false;
    static int turn_output = 1024;
    static int throttle_output = 1024;
    static unsigned long lastDataReceived = 0;
    static unsigned long dataPacketCount = 0;
    static bool dataReceived = false;

    // 读取SBUS信号
    bool newDataReceived = sbus_rx.Read();
    if (newDataReceived)
    {
        sbus_data = sbus_rx.data();
        lastDataReceived = millis();
        dataPacketCount++;
        dataReceived = true;

        // 获取Channel1(油门左)和Channel2(油门右)
        int throttle_left = sbus_data.ch[0];  // Channel 1
        int throttle_right = sbus_data.ch[1]; // Channel 2

        // 差速控制转换
        differentialToTankControl(throttle_left, throttle_right, turn_output, throttle_output);

        // 初始化所有通道为中位值
        for (int i = 0; i < 16; i++)
        {
            sbus_tx_data.ch[i] = 1024; // 中位值
        }

        // 只设置前后和左右两个控制通道
        sbus_tx_data.ch[0] = turn_output;     // Channel 1: 左右控制
        sbus_tx_data.ch[2] = throttle_output; // Channel 3: 前后控制

        // 复制失联和故障安全状态
        sbus_tx_data.lost_frame = sbus_data.lost_frame;
        sbus_tx_data.failsafe = sbus_data.failsafe;

        // 设置转换后的数据并发送
        sbus_tx.data(sbus_tx_data);
        sbus_tx.Write();
    }

    // LED闪烁控制 - 正常工作时每500ms闪烁一次
    if (millis() - lastLedToggle >= 500)
    {
        lastLedToggle = millis();
        ledState = !ledState;
        digitalWrite(pin_definition.led_pin, ledState);
    }

    // 每100ms打印一次转换后的控制值
    if (millis() - lastPrint >= 100)
    {
        lastPrint = millis();

        // 只打印前后和左右两个通道的值
        Serial.print("前后:");
        Serial.print(throttle_output);
        Serial.print(" 左右:");
        Serial.print(turn_output);

        // 显示原始输入值
        Serial.print(" | 输入 - 左:");
        Serial.print(sbus_data.ch[0]);
        Serial.print(" 右:");
        Serial.print(sbus_data.ch[1]);

        Serial.println();
    }

    // 故障安全处理
    if (sbus_data.lost_frame || sbus_data.failsafe)
    {
        // 失联或故障安全时，Channel1和Channel3输出中位值停止小车
        // 其他通道保持最后的有效值
        sbus_tx_data.ch[0] = 1024; // 左右控制停止
        sbus_tx_data.ch[2] = 1024; // 前后控制停止

        sbus_tx.data(sbus_tx_data);
        sbus_tx.Write();

        // 故障安全时LED快速闪烁警告 - 每100ms闪烁一次
        if (millis() - lastLedToggle >= 200)
        {
            lastLedToggle = millis();
            ledState = !ledState;
            digitalWrite(pin_definition.led_pin, ledState);
        }
    }

    delay(1);
}
