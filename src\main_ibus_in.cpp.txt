#include <Arduino.h>
#include <HardwareSerial.h>
#include <IBusBM.h>

// IBUS输入测试打印程序

typedef struct
{
    int8_t ibus_rx_pin;
    int8_t ibus_tx_pin;
    int8_t led_pin;
} PinDefinition;

// 结构体实例化 - 独立的IBUS引脚
PinDefinition pin_definition = {
    .ibus_rx_pin = 20, // IBUS专用接收引脚（与SBUS分离）
    .ibus_tx_pin = 21, // IBUS专用发送引脚
    .led_pin = 8       // 状态指示LED
};

HardwareSerial SerialIbus(2); // 硬件串口2用于IBUS（与SBUS分离）

IBusBM IBus; // IBus对象

void setup()
{
    // 初始化USB串口用于调试输出
    Serial.begin(115200);
    pinMode(pin_definition.led_pin, OUTPUT);

    // 初始化IBUS通信
    SerialIbus.begin(115200, SERIAL_8N1, pin_definition.ibus_rx_pin, pin_definition.ibus_tx_pin);
    IBus.begin(SerialIbus, IBUSBM_NOTIMER);

    Serial.println("=== IBUS信号测试程序 ===");
    Serial.println("等待IBUS信号输入...");
    Serial.println("引脚配置:");
    Serial.print("  IBUS RX: GPIO");
    Serial.println(pin_definition.ibus_rx_pin);
    Serial.print("  IBUS TX: GPIO");
    Serial.println(pin_definition.ibus_tx_pin);
    Serial.print("  LED: GPIO");
    Serial.println(pin_definition.led_pin);
    Serial.println("========================");
}

void loop()
{
    static unsigned long lastPrint = 0;
    static bool ledState = false;

    // 调用IBus的内部循环函数
    IBus.loop();

    // 每200ms打印一次数据
    if (millis() - lastPrint >= 500)
    {
        lastPrint = millis();

        // 打印所有通道的值
        Serial.print("IBUS通道: ");
        for (int i = 0; i < 10; i++) // IBUS通常有10个通道
        {
            int channelValue = IBus.readChannel(i);
            Serial.print("CH");
            Serial.print(i + 1);
            Serial.print(":");
            Serial.print(channelValue);
            Serial.print(" ");
        }

        // 打印接收计数器
        Serial.print("| 接收计数: ");
        Serial.print(IBus.cnt_rec);
        Serial.print(" 传感器计数: ");
        Serial.print(IBus.cnt_sensor);
        Serial.println();

        // LED指示数据接收
        if (IBus.cnt_rec > 0)
        {
            ledState = !ledState;
            digitalWrite(pin_definition.led_pin, ledState);
        }
        else
        {
            digitalWrite(pin_definition.led_pin, LOW);
        }
    }

    delay(1);
}
